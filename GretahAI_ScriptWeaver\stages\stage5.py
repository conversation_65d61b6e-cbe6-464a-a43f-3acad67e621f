"""
Stage 5: Test Data Configuration for GretahAI ScriptWeaver

This module handles the test data configuration phase of the application workflow.
It provides functionality for:
- Auto-generating test data based on step analysis and detected elements
- Manual test data entry for specific test steps
- Test data categorization and display
- Integration with test_data_manager for data generation

The stage supports both automatic test data generation using AI analysis
and manual data entry through user input fields. Test data is properly
categorized and stored in the state manager for use in script generation.

Functions:
    stage5_test_data(state): Main Stage 5 function for test data configuration
    _display_generated_test_data(state, step_test_data): Helper function for displaying test data
"""

import logging
import streamlit as st
from state_manager import StateStage

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage5")


def stage5_test_data(state):
    """
    Phase 5: Configure Test Data for Selected Test Case Step.

    This function handles the test data configuration phase, allowing users to either
    auto-generate test data based on step analysis or manually enter test data.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Phase 5: Configure Test Data</h2>", unsafe_allow_html=True)

    # Check if we have a selected step and matched elements
    llm_step_analysis = getattr(state, 'llm_step_analysis', {})
    test_data_skipped = getattr(state, 'test_data_skipped', False)

    # Ensure step_elements is available for test data generation
    if hasattr(state, 'selected_step') and state.selected_step:
        if not hasattr(state, 'step_elements') or not state.step_elements:
            # Fallback: use detected_elements or qa_relevant_elements if available
            if hasattr(state, 'qa_relevant_elements') and state.qa_relevant_elements:
                state.step_elements = state.qa_relevant_elements
            elif hasattr(state, 'detected_elements') and state.detected_elements:
                state.step_elements = state.detected_elements
            else:
                # Create empty list as last resort
                state.step_elements = []

    # Check if test data has already been automatically skipped
    if test_data_skipped and not llm_step_analysis.get("requires_test_data", True):
        st.success("✓ Test data configuration skipped")

        # Add indicator to sidebar
        with st.sidebar:
            st.info(f"No test data needed for this step")
            st.success("Ready for Phase 6")

    # Check if prerequisites are met
    elif not hasattr(state, 'selected_step') or not hasattr(state, 'step_matches'):
        st.warning("⚠️ Please complete Phase 4 first")

    # Check if test data is required based on analysis
    elif not llm_step_analysis.get("requires_test_data", True):
        with st.expander("Test Data Status", expanded=True):
            st.success("✓ No test data required")
            st.info(f"Reason: {llm_step_analysis.get('test_data_reason', 'No data needed for this step type.')}")

            # Provide a button to skip directly to script generation
            if st.button("Proceed to Phase 6", key="skip_to_script_gen"):
                # Set a flag in state manager to indicate test data was skipped but it's OK
                state.test_data_skipped = True
                state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
                state.step_ready_for_script = True  # Mark the step as ready for script generation

                # Add indicator to sidebar
                with st.sidebar:
                    st.success("✓ Ready for Phase 6")

                st.success("✓ Proceeding to Phase 6")
                st.rerun()  # Refresh the page to update the UI
    else:
        # Test data is required - show the configuration UI
        with st.expander("Test Data Configuration", expanded=True):
            st.info(f"This step requires test data: {llm_step_analysis.get('test_data_reason', 'Data input needed')}")

            # Add a button to skip test data configuration if needed
            if st.button("Skip Test Data", key="force_skip_test_data", help="Skip test data configuration and proceed to Phase 6"):
                # Set a flag in state manager to indicate test data was skipped
                state.test_data_skipped = True
                state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
                state.step_ready_for_script = True  # Mark the step as ready for script generation

                # Add indicator to sidebar
                with st.sidebar:
                    st.success("✓ Ready for Phase 6")

                st.success("✓ Test data skipped")

                # Automatically advance to Stage 6 since test data was skipped
                if state.current_stage == StateStage.STAGE5_DATA:
                    from state_manager import StateStage
                    state.advance_to(StateStage.STAGE6_GENERATE, "Test data skipped - advancing to Stage 6")

                    # Force state update in session state
                    st.session_state['state'] = state
                    st.session_state['stage_progression_message'] = "✅ Test data skipped. Proceeding to Stage 6."

                    # Call st.rerun() to immediately refresh the UI
                    st.rerun()
                    return

                st.rerun()  # Refresh the page to update the UI

        # Create tabs for different test data options in a separate collapsible section
        with st.expander("Test Data Options", expanded=True):
            # If we have specific data types identified, show them in a compact format
            data_types = llm_step_analysis.get('data_types', [])
            if data_types:
                st.markdown("**Detected Data Types:**")
                for data_type in data_types:
                    st.markdown(f"- {data_type}")

            # Create tabs for different test data options
            auto_tab, manual_tab = st.tabs(["🤖 Auto-Generate", "✏️ Manual Entry"])

            with auto_tab:
                # Add a button to generate test data for the selected step
                if st.button("Generate Test Data", help="Generate test data automatically for this step"):
                    selected_step = state.selected_step
                    step_elements = state.step_elements

                    if not selected_step:
                        st.error("Please complete Phase 4 first")
                    elif not step_elements and not hasattr(state, 'detected_elements') and not hasattr(state, 'qa_relevant_elements'):
                        st.error("Please detect UI elements in Phase 4 first")
                    else:
                        # Ensure step_elements is available (use detected_elements as fallback)
                        if not step_elements:
                            if hasattr(state, 'qa_relevant_elements') and state.qa_relevant_elements:
                                step_elements = state.qa_relevant_elements
                            elif hasattr(state, 'detected_elements') and state.detected_elements:
                                step_elements = state.detected_elements
                            else:
                                step_elements = []
                            # Update session state
                            state.step_elements = step_elements

                        with st.spinner("Generating test data..."):
                            try:
                                from test_data_manager import generate_test_data_for_step

                                # Prepare step data for test data generation
                                step_data = {
                                    "action": selected_step.get('Test Steps', ''),
                                    "expected": selected_step.get('Expected Result', '')
                                }

                                # Get step table entry if available
                                step_table_entry = None
                                if hasattr(state, 'selected_step_table_entry') and state.selected_step_table_entry:
                                    step_table_entry = state.selected_step_table_entry

                                # Generate test data for the specific step
                                step_test_data = generate_test_data_for_step(step_data, step_elements, step_table_entry)

                                # Store in state manager
                                if not hasattr(state, 'test_data') or not isinstance(state.test_data, dict):
                                    state.test_data = {}

                                # Update state manager with step-specific test data
                                state.test_data.update(step_test_data)
                                state.step_ready_for_script = True  # Mark the step as ready for script generation

                                # Add indicator to sidebar
                                with st.sidebar:
                                    st.success("✓ Test data generated")
                                    st.info("Ready for Phase 6")

                                # Display the generated test data
                                st.success(f"✓ Generated test data for Step {selected_step.get('Step No')}")

                                _display_generated_test_data(state, step_test_data)

                                # Automatically advance to Stage 6 since test data was generated
                                if state.current_stage == StateStage.STAGE5_DATA:
                                    from state_manager import StateStage
                                    state.advance_to(StateStage.STAGE6_GENERATE, "Test data generated - advancing to Stage 6")

                                    # Force state update in session state
                                    st.session_state['state'] = state
                                    st.session_state['stage_progression_message'] = "✅ Test data generated. Proceeding to Stage 6."

                                    # Call st.rerun() to immediately refresh the UI
                                    st.rerun()
                                    return
                            except Exception as e:
                                st.error(f"Error generating test data: {e}")

            with manual_tab:
                # Initialize manual test data in state if not exists
                if not hasattr(state, 'manual_test_data') or not isinstance(state.manual_test_data, dict):
                    state.manual_test_data = {}

                # Get the current step information
                selected_step = state.selected_step
                if selected_step:
                    step_no = selected_step.get('Step No', 'Unknown')
                    step_action = selected_step.get('Test Steps', '')

                    # Display step information in a compact format
                    st.markdown(f"**Step {step_no}**")

                    # Create a unique key for this step
                    step_key = f"step_{step_no}"

                    # Get existing value if any
                    existing_value = state.manual_test_data.get(step_key, "")

                    # Create input field for manual data entry
                    manual_value = st.text_input(
                        f"Enter data for Step {step_no}:",
                        value=existing_value,
                        key=f"manual_data_{step_no}",
                        help="Enter test data to use during test execution"
                    )

                    # Save the entered value to state when changed
                    if manual_value != existing_value:
                        state.manual_test_data[step_key] = manual_value
                        logger.info(f"State change: manual_test_data[{step_key}] = {manual_value}")

                        # Also add to the main test_data dictionary with a prefix to identify it
                        if not hasattr(state, 'test_data') or not isinstance(state.test_data, dict):
                            state.test_data = {}

                        # Only add non-empty values to test_data
                        if manual_value.strip():
                            # Create a more descriptive parameter name
                            param_name = f"manual_input_for_step_{step_no}"

                            # Add to both manual_test_data and test_data
                            state.manual_test_data[step_key] = manual_value
                            state.test_data[param_name] = manual_value

                            # Also add a more generic parameter name
                            action_text = selected_step.get('Test Steps', '').lower()

                            # Try to create a more meaningful parameter name based on the action
                            if any(word in action_text for word in ['search', 'find', 'query']):
                                state.test_data['search_query'] = manual_value
                            elif any(word in action_text for word in ['enter', 'input', 'type', 'fill']):
                                if any(word in action_text for word in ['email']):
                                    state.test_data['email'] = manual_value
                                elif any(word in action_text for word in ['password']):
                                    state.test_data['password'] = manual_value
                                elif any(word in action_text for word in ['name']):
                                    state.test_data['name'] = manual_value
                                elif any(word in action_text for word in ['username']):
                                    state.test_data['username'] = manual_value
                                else:
                                    state.test_data['input_text'] = manual_value

                            logger.info(f"State change: test_data[{param_name}] = {manual_value}")
                            state.step_ready_for_script = True  # Mark the step as ready for script generation

                            # Add indicator to sidebar
                            with st.sidebar:
                                st.success("✓ Test data saved")
                                st.info("Ready for Phase 6")

                            # Show success message
                            st.success(f"✓ Data saved for Step {step_no}")

                            # Automatically advance to Stage 6 since test data was saved
                            if state.current_stage == StateStage.STAGE5_DATA:
                                from state_manager import StateStage
                                state.advance_to(StateStage.STAGE6_GENERATE, "Manual test data saved - advancing to Stage 6")

                                # Force state update in session state
                                st.session_state['state'] = state
                                st.session_state['stage_progression_message'] = "✅ Test data saved. Proceeding to Stage 6."

                                # Call st.rerun() to immediately refresh the UI
                                st.rerun()
                                return
                        elif step_key in state.manual_test_data:
                            # Remove the parameter if it exists and the value is now empty
                            param_name = f"manual_input_for_step_{step_no}"

                            # Remove from both dictionaries
                            del state.manual_test_data[step_key]

                            # Remove from test_data if it exists
                            if param_name in state.test_data:
                                del state.test_data[param_name]

                            # Also try to remove any generic parameters we might have added
                            for key in ['search_query', 'email', 'password', 'name', 'username', 'input_text']:
                                if key in state.test_data and state.test_data[key] == existing_value:
                                    del state.test_data[key]

                            logger.info(f"State change: Removed manual_test_data[{step_key}] and related test_data entries")

                    # Display current manual test data if available
                    if state.manual_test_data and any(value.strip() for value in state.manual_test_data.values()):
                        st.markdown("**Current Test Data:**")
                        # Create a table for better visualization
                        data = []
                        for key, value in state.manual_test_data.items():
                            if value.strip():  # Only show non-empty values
                                step_num = key.split('_')[1]
                                # Show both the specific and generic parameter names
                                specific_param = f"manual_input_for_step_{step_num}"

                                # Try to determine the generic parameter based on the action
                                action_text = ""
                                if hasattr(state, 'step_table_json') and state.step_table_json:
                                    for step_entry in state.step_table_json:
                                        if step_entry.get('step_no') == step_num:
                                            action_text = step_entry.get('action', '').lower()
                                            break

                                generic_param = "input_text"  # Default
                                if action_text:
                                    if any(word in action_text for word in ['search', 'find', 'query']):
                                        generic_param = "search_query"
                                    elif any(word in action_text for word in ['enter', 'input', 'type', 'fill']):
                                        if any(word in action_text for word in ['email']):
                                            generic_param = "email"
                                        elif any(word in action_text for word in ['password']):
                                            generic_param = "password"
                                        elif any(word in action_text for word in ['name']):
                                            generic_param = "name"
                                        elif any(word in action_text for word in ['username']):
                                            generic_param = "username"

                                data.append({
                                    "Step": f"Step {step_num}",
                                    "Value": value,
                                    "Used As": generic_param
                                })

                        if data:
                            # Convert to DataFrame for display
                            import pandas as pd
                            df = pd.DataFrame(data)
                            st.table(df)
                else:
                    st.warning("Please complete Phase 4 first")


def _display_generated_test_data(state, step_test_data):
    """
    Display the generated test data in a visually appealing way.

    This function categorizes test data into different types (user, contact, payment, etc.)
    and displays them in organized tabs for better user experience.

    Args:
        state (StateManager): The application state manager instance
        step_test_data (dict): Dictionary containing the generated test data
    """
    # Group test data by categories for better organization
    user_data = {}
    contact_data = {}
    payment_data = {}
    form_data = {}
    manual_data = {}
    other_data = {}

    # Categorize the test data
    for key, value in step_test_data.items():
        key_lower = key.lower()
        if 'manual_param' in key_lower:
            manual_data[key] = value
        elif any(word in key_lower for word in ['email', 'user', 'name', 'password', 'login']):
            user_data[key] = value
        elif any(word in key_lower for word in ['phone', 'mobile', 'address', 'city', 'state', 'zip', 'country']):
            contact_data[key] = value
        elif any(word in key_lower for word in ['card', 'payment', 'cvv', 'expiry', 'credit']):
            payment_data[key] = value
        elif any(word in key_lower for word in ['input', 'field', 'form', 'text', 'select']):
            form_data[key] = value
        else:
            other_data[key] = value

    # Display data in categories using tabs
    if step_test_data:
        st.markdown("**Generated Test Data:**")

        # Create tabs for different data categories
        tab_names = []
        tab_data = []

        if manual_data:
            tab_names.append("✏️ Manual")
            tab_data.append(manual_data)

        if user_data:
            tab_names.append("👤 User")
            tab_data.append(user_data)

        if contact_data:
            tab_names.append("📱 Contact")
            tab_data.append(contact_data)

        if payment_data:
            tab_names.append("💳 Payment")
            tab_data.append(payment_data)

        if form_data:
            tab_names.append("📝 Form")
            tab_data.append(form_data)

        if other_data:
            tab_names.append("🔄 Other")
            tab_data.append(other_data)

        # If we have data to display, create tabs
        if tab_names:
            tabs = st.tabs(tab_names)

            # Display data in each tab
            for i, (tab, data_dict) in enumerate(zip(tabs, tab_data)):
                with tab:
                    for key, value in data_dict.items():
                        # Extract step number from the key for manual data
                        if 'step_' in key and tab_names[i] == "✏️ Manual":
                            step_num = key.split('step_')[1]
                            st.markdown(f"**Step {step_num}:** `{value}`")
                        else:
                            st.markdown(f"**{key}:** `{value}`")
        else:
            st.info("No test data generated")
    else:
        st.info("No test data generated")
